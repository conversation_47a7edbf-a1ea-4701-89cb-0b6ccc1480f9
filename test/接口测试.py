import pprint
import requests
import json
import datetime

# 滑动窗口三晚连住查询函数

def get_room_price_sliding_window(hotel_code: str, days: int = 30, target_room_name="豪华房（南楼）(大床)") -> dict:
    """
    滑动窗口三晚连住查询：每次查checkIn=当天，checkOut=当天+3天，窗口每天滑动，保证每一天都能拿到三晚连住价。
    只保留check_in当天的价格。
    :param hotel_code: 酒店code
    :param days: 查询天数
    :param target_room_name: 目标房型名
    :return: {date_str: price}
    """
    result = {}
    today = datetime.date.today()
    url = "http://139.199.168.35:8080/getPrice"
    for i in range(days - 2):  # 最后两天无法再查三晚
        check_in = today + datetime.timedelta(days=i)
        check_out = check_in + datetime.timedelta(days=3)
        payload = json.dumps({
            "hotelCode": hotel_code,
            "checkInDate": check_in.strftime('%Y-%m-%d'),
            "checkOutDate": check_out.strftime('%Y-%m-%d'),
            "personCount": "1",
            "roomCount": "1",
            "channel": "同城"
        })
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("GET", url, headers=headers, data=payload)
        try:
            resp_json = json.loads(response.text)
            data = resp_json.get('data', {})
            room_list = data.get("roomList", [])
            if not room_list:
                continue
            target_room = None
            for room in room_list:
                if room.get("roomName") == target_room_name:
                    target_room = room
                    break
            if not target_room:
                print(f"未找到房型：{target_room_name}")
                continue
            price_list = target_room.get("priceList", [])
            if price_list:
                avg_price_list = price_list[0].get("averagePriceList", [])
                # 只取本次窗口的第一天（check_in）的价格
                if avg_price_list:
                    for day_price in avg_price_list:
                        for date_str, price in day_price.items():
                            if date_str == check_in.strftime('%Y-%m-%d'):
                                result[date_str] = int(float(price))
        except Exception as e:
            print(f"解析接口数据出错: {e}")
    return result

if __name__ == "__main__":
    # ====== 参数设置 ======
    hotel_code = "woqxxa682525"  # 你的酒店code
    target_room_name = "豪华房（南楼）(大床)"  # 你的目标房型名
    days = 30  # 查询天数

    # ====== 查询并输出 ======
    price_data = get_room_price_sliding_window(hotel_code, days, target_room_name)
    print("\n滑动窗口30天价格数据：")
    pprint.pprint(price_data)
