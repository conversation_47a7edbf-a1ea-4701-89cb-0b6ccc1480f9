import requests
import json
import datetime
import time
from DrissionPage import ChromiumPage
from DrissionPage._functions.keys import Keys
from DrissionPage.common import Actions

# 查找并进入酒店函数

def find_and_enter_hotel(page, hotel_name):
    """
    在同程后台页面自动搜索并进入指定酒店的房型维护页面。
    :param page: DrissionPage对象
    :param hotel_name: 目标酒店名称
    """
    # 点击酒店名称（弹出输入框）
    page.ele('x://*[@id="common_app"]/div/div[2]/div/div[2]/span[1]/span/div/span').click()
    time.sleep(1)
    # 点击酒店输入框
    input_line = page.ele('x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/div[5]/div/input')
    input_line.click()
    time.sleep(1)
    # 输入酒店名称
    input_line.input(hotel_name)
    time.sleep(1)
    # 选择下拉备选项
    page.ele('.el-tooltip option-text item').click()
    time.sleep(1)
    # 点击查询
    search_button = page.ele('x://*[@id="common_app"]/div/div[4]/div/div[2]/div/div[1]/button')
    search_button.click()
    time.sleep(1)
    # 点击查询到的酒店名称
    page.ele('.w-1/3 pt-1.0 pb-1.0 text-14 cursor-pointer').click()
    time.sleep(1)  # 等待跳转

class PriceMaintainer:
    def __init__(self, page, hotel_code, hotel_name, days=30, tongcheng_hotel_room_name=None):
        self.page = page
        self.hotel_code = hotel_code
        self.hotel_name = hotel_name
        self.days = days
        self.start_index = None  # "今日"所在th的列号
        self.actions = Actions(page)
        # 新增：保存同程房型名
        self.tongcheng_hotel_room_name = tongcheng_hotel_room_name
    def clear_overlays(self):

        overlay_selectors = [
            '.el-message-box__close.el-icon-close',
            '.el-dialog__headerbtn',
            '.el-overlay',
        ]
        for sel in overlay_selectors:
            try:
                btn = self.page.ele(sel)
                if btn:
                    btn.click()
                    print(f'已关闭遮挡层/弹窗: {sel}')
                    time.sleep(0.5)
            except Exception:
                continue

    def find_ele_with_retry(self, page, selector, max_retry=2, interval=0.5, clear_overlay_func=None):
        """
        查找元素，失败时自动重试，若有清理遮挡层方法则自动调用。
        增加详细日志，便于排查误点。
        """
        for i in range(max_retry):
            ele = page.ele(selector, timeout=2)
            if ele:
                print(f"[find_ele_with_retry] 第{i+1}次找到元素: {selector}, text={getattr(ele, 'text', None)}, url={getattr(page, 'url', None)}")
                return ele
        print(f"[find_ele_with_retry] 未找到元素: {selector}, url={getattr(page, 'url', None)}")
        return None

    def get_today_col_index(self):
        """
        查找表头"今日"所在的th列索引（从1开始）
        """
        for i in range(2, 4):
            try:
                text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                if text == '今日':
                    print('开始元素为：', i)
                    self.start_index = i
                    return i
            except Exception:
                continue
        raise Exception('未找到"今日"所在的表头列')

    def is_cell_invalid(self, row, col):
        """
        判断指定单元格是否无效，参考多xpath判断方式
        """
        try:
            price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]/span/span/div/p[2]'
            price_text = self.page.ele(price_text_xpath, timeout=0.5).text
            print(f"第{row}行第{col}格有价格: {price_text}")
            return False
        except Exception:
            try:
                price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]/p'
                price_text = self.page.ele(price_text_xpath, timeout=0.5).text
                print(f"第{row}行第{col}格无效价格: {price_text}")
                return price_text.strip() == '无效'
            except Exception:
                try:
                    price_text_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row}]/td[{col}]/div[3]'
                    price_text = self.page.ele(price_text_xpath, timeout=0.5).text
                    print(f"第{row}行第{col}格基础获取: {price_text}")
                    return price_text.strip() == '无效'
                except Exception:
                    print(f"第{row}行第{col}格无法获取价格信息")
                    return False

    def close_popup(self, date_str):
        """
        关闭价格维护弹窗，若多次失败则刷新页面。date_str用于日志输出。
        """
        max_retry = 5
        success = False
        for i in range(max_retry):
            close_btn = self.find_ele_with_retry(self.page, '.el-button el-button--default el-button--small el-button--primary ', max_retry=1, interval=0.5, clear_overlay_func=self.clear_overlays)
            if close_btn:
                try:
                    close_btn.click()
                    print(f"已点击 class 关闭按钮 {date_str}")
                    time.sleep(0.5)
                    break
                except Exception as e:
                    print(f"点击 class 关闭按钮异常: {e}，重试")
            else:
                page.ele('.').input(Keys.ENTER)

    def should_set_invalid(self, real_price):
        """
        判断价格是否需要设置为无效（如8888、9999）
        """
        return real_price in (8888, 9999)

    def click_set_price_valid(self):
        """
        点击"设置价格有效"按钮
        """
        valid_btn = None
        btns = self.page.eles('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain')
        for btn in btns:
            try:
                if btn.text and "设置价格有效" in btn.text:
                    valid_btn = btn
                    break
            except Exception:
                continue
        if not valid_btn:
            # retry 用 find_ele_with_retry
            valid_btn = self.find_ele_with_retry(
                self.page,
                '.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain',
                clear_overlay_func=self.clear_overlays
            )
            if valid_btn and (not valid_btn.text or "设置价格有效" not in valid_btn.text):
                valid_btn = None
        if valid_btn:
            valid_btn.click()
            print(f"已点击'设置价格有效'按钮，等待页面刷新")
            time.sleep(0.5)

    def click_set_price_invalid(self):
        """
        点击"设置价格无效"按钮
        """
        invalid_btn = None
        for _ in range(5):
            btns = self.page.eles('.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain')
            for btn in btns:
                try:
                    if btn.text and "设置价格无效" in btn.text:
                        invalid_btn = btn
                        break
                except Exception:
                    continue
            if invalid_btn:
                break
            # retry 用 find_ele_with_retry
            retry_btn = self.find_ele_with_retry(
                self.page,
                '.el-button mb-[10px] w-[142px] el-button--primary el-button--medium is-plain',
                clear_overlay_func=self.clear_overlays
            )
            if retry_btn and retry_btn.text and "设置价格无效" in retry_btn.text:
                invalid_btn = retry_btn
                break
            time.sleep(0.5)
        if invalid_btn:
            invalid_btn.click()
            print(f"设置为无效")
            time.sleep(2)
        else:
            print(f"未找到设置价格无效按钮")

    def currency_cny_to_hkd(self, price_cny, rate=0.91):
        """
        人民币价格转换为港币价格，默认汇率0.86。
        :param price_cny: 人民币价格
        :param rate: 汇率，默认0.86
        :return: 港币价格（四舍五入取整）
        """
        try:
            return int(round(float(price_cny) / rate))
        except Exception:
            return price_cny

    def generate_price_matrix(self, price_data, rows=4):
        """
        生成斜三角三晚连住+关房价格矩阵。
        :param price_data: {date_str: price}
        :param rows: 行数，默认4
        :return: matrix, date_list
        """
        date_list = list(price_data.keys())
        n = len(date_list)
        matrix = [[9999 for _ in range(n)] for _ in range(rows)]  # 默认全部9999
        for row in range(rows):
            col = row  # 每行从第row个格子开始
            while col < n:
                # 连住三晚
                for offset in range(3):
                    c = col + offset
                    if c >= n:
                        break
                    price = price_data[date_list[col]]
                    real_price = 9999 if price is None else price
                    matrix[row][c] = real_price
                # 关房
                block_col = col + 3
                if block_col < n:
                    matrix[row][block_col] = 9999
                col += 4
        return matrix, date_list
    def fill_single_cell(self, row, page_col, cell_col, real_price, date_str, page_idx=0):
        """
        负责单元格的点击、输入、保存、关闭弹窗、设置无效等
        :param page_idx: 当前页索引，0为第一页
        """
        cell_start = time.time()
        print(f"第{row+1}行，第{cell_col}格（本页第{page_col}格），日期{date_str}，填入价格: {real_price}")
        td_col = 2 + page_col  # 每一页都从2开始递增
        price_box_xpath = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]'
        price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
        if not price_box:
            print(f"未找到价格框 {date_str}")
            return
        price_box.click()
        if real_price is None or real_price == 9999:
            print(f"{date_str} 目标为None或9999，直接设置为无效")
            self.click_set_price_invalid()
            return
        real_price_hkd = self.currency_cny_to_hkd(real_price)
        if self.is_cell_invalid(row+2, td_col):
            self.click_set_price_valid()
        price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
        if not price_box:
            print(f"未找到价格框 {date_str}")
            return
        price_box.click()
        price_input_xpath = 'x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[1]/form/div[6]/div/div/div/div[1]/div[1]/div/div/div[1]/input'
        self.page.ele(price_input_xpath, timeout=2)
        modify_btn = self.find_ele_with_retry(self.page, 'x://button[@class="el-button mb-[10px] w-[142px] el-button--primary el-button--medium"]', clear_overlay_func=self.clear_overlays)
        if not modify_btn:
            print(f"未找到修改价格按钮 {date_str}")
            return
        modify_btn.click()
        self.page.ele(price_input_xpath, timeout=2)
        price_input = self.find_ele_with_retry(self.page, price_input_xpath, clear_overlay_func=self.clear_overlays)
        if not price_input:
            print(f"未找到价格输入框 {date_str}")
            return
        price_input.clear()
        price_input.input(str(real_price_hkd))
        save_btn_xpath = 'x://*[@id="product-app"]/div/div[3]/div[4]/div[2]/div/div[2]/div[2]/button[2]'
        save_btn = self.find_ele_with_retry(self.page, save_btn_xpath, clear_overlay_func=self.clear_overlays)
        if not save_btn:
            print(f"未找到保存按钮 {date_str}")
            return
        save_btn.click()
        print(f"已保存 {date_str} 价格: {real_price_hkd}")
        time.sleep(2)
        self.close_popup(date_str)
        if self.should_set_invalid(real_price):
            print(f"{date_str} 价格为{real_price}，自动设置为无效")
            price_box = self.find_ele_with_retry(self.page, price_box_xpath, clear_overlay_func=self.clear_overlays)
            if price_box:
                price_box.click()
                self.click_set_price_invalid()
            else:
                print(f"再次点击价格框失败，无法设置无效 {date_str}")
        time.sleep(0.5)
    def get_actual_price_matrix(self, date_list, return_page_idx=False):
        """
        批量采集页面所有价格信息，自动翻页，生成完整实际价格矩阵。
        :param date_list: 目标日期列表，决定采集天数和矩阵宽度
        :param return_page_idx: 是否返回本轮实际翻页数
        返回: matrix[row][col]，内容为数字或9999（无效/无房/None），如return_page_idx为True则返回(matrix, page_idx)
        采集时第一页只从"今日"所在td_col（start_index）开始，跳过td[2]（昨天）；后续页从td[2]开始采集。
        """
        # 新增：一开始就选择房型
        if self.tongcheng_hotel_room_name:
            self.select_room_by_name(self.tongcheng_hotel_room_name)
            print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 已选择房型：{self.tongcheng_hotel_room_name}")
            time.sleep(2)  # 等待页面刷新
        rows = 4
        n = len(date_list)
        matrix = [[None for _ in range(n)] for _ in range(rows)]
        page_start = 0
        page_idx = 0
        while page_start < n:
            if page_idx == 0:
                格数 = min(15 - self.start_index + 1, n - page_start)
            else:
                格数 = min(14, n - page_start)
            for row in range(rows):
                for page_col in range(格数):
                    td_col = 2 + page_col  # 每一页都从2开始递增
                    col = page_start + page_col  # 全局列号
                    if col >= n:
                        continue
                    xpath_num = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]/span/span/div/p[2]'
                    xpath_invalid = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]/p'
                    xpath_base = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{td_col}]/div[3]'
                    val = None
                    try:
                        val = self.page.ele(xpath_num, timeout=0.5).text
                    except Exception:
                        try:
                            val = self.page.ele(xpath_invalid, timeout=0.5).text
                        except Exception:
                            try:
                                val = self.page.ele(xpath_base, timeout=0.5).text
                            except Exception:
                                val = None
                    if val is None or (isinstance(val, str) and (val.strip() == '无效' or val.strip() == '无房' or val.strip() == '')):
                        matrix[row][col] = 9999
                    else:
                        try:
                            matrix[row][col] = int(val)
                        except Exception:
                            matrix[row][col] = val
            page_start += 格数
            page_idx += 1
            if page_start < n:
                print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 点击后两周，当前page_idx={page_idx}")
                self.click_next_two_weeks()
                time.sleep(2)
        for i in range(page_idx - 1):
            print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 点击前两周({i+1}/{page_idx-1})，准备回到第一页")
            self.click_prev_two_weeks()
        # 新增：回到第一页后选择房型并等待，输出详细日志
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 已回到第一页，准备选择房型")
        if self.tongcheng_hotel_room_name:
            self.select_room_by_name(self.tongcheng_hotel_room_name)
            print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 已选择房型：{self.tongcheng_hotel_room_name}")
            time.sleep(2)  # 等待页面刷新
        if return_page_idx:
            return matrix, page_idx
        return matrix

    def is_price_close(self, target, actual, threshold=0.05):
        """
        判断两个价格相差是否在阈值百分比以内。
        :param target: 目标价格
        :param actual: 实际价格
        :param threshold: 百分比阈值，默认5%
        :return: True表示相差在阈值内
        """
        try:
            target = int(target)
            actual = int(actual)
            if target == 0:
                return actual == 0
            return abs(target - actual) / abs(target) <= threshold
        except Exception:
            return False

    def compare_and_fill_matrix(self, matrix, actual_matrix, date_list):
        """
        比对目标矩阵和实际矩阵，仅对不一致且不在5%以内的格子进行修改。
        目标为None/9999且实际为9999/None则无需操作，目标为None/9999且实际为数字则设置为无效。
        比较价格时，目标价格先换算为港币。
        日志中打印原始目标价格、换算后港币价格、实际格子价格、最终要填入的价格。
        修正：比对时用col=page_start+(page_col-1)作为全局目标矩阵的索引，td_col第一页和后续页不同。
        新增：返回本轮是否有格子被修改（has_change）。
        新增：严格对齐输出所有需要被修改的格子。
        """
        n = len(date_list)
        rows = 4
        page_start = 0
        page_idx = 0
        has_change = False  # 标志位：本轮是否有格子被修改
        need_modify_cells = []  # 新增：收集所有需要被修改的格子
        while page_start < n:
            if page_idx == 0:
                格数 = min(15 - self.start_index + 1, n - page_start)
            else:
                格数 = min(14, n - page_start)
            print("\n=== 实际采集到的价格矩阵 ===")
            for row in range(rows):
                row_prices = []
                for col in range(n):
                    if col < len(actual_matrix[row]):
                        val = actual_matrix[row][col]
                        row_prices.append(str(val))
                    else:
                        row_prices.append("-")
                print(f"第{row+1}行: ", ' '.join(row_prices))
            print("=== 实际矩阵打印结束 ===\n")
            for row in range(rows):
                for page_col in range(格数):
                    col = page_start + page_col
                    if col >= n:
                        continue
                    target_val = matrix[row][col]
                    actual_val = actual_matrix[row][col]  # 全局列号对齐
                    date_str = date_list[col]
                    if target_val is None or str(target_val) == '9999':
                        if actual_val is None or str(actual_val) == '9999':
                            continue
                        else:
                            # 需设无效
                            need_modify_cells.append({
                                'row': row+1,
                                'col': col+1,
                                'date': date_str,
                                'target': target_val,
                                'actual': actual_val,
                                'final': target_val
                            })
                            print(f"[需设无效] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），目标: {target_val}，实际: {actual_val}")
                            self.fill_single_cell(row, page_col, col+1, target_val, date_str, page_idx)
                            has_change = True
                        continue
                    target_val_hkd = self.currency_cny_to_hkd(target_val)
                    if str(target_val_hkd) != str(actual_val) and not (str(target_val_hkd) == '9999' and str(actual_val) == '9999'):
                        if self.is_price_close(target_val_hkd, actual_val):
                            # 新增：价格相差在5%以内但无需修改的日志
                            print(f"[无需修改] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），目标: {target_val_hkd}，实际: {actual_val}，差值在5%以内，无需修改")
                            continue
                        # 需修改
                        need_modify_cells.append({
                            'row': row+1,
                            'col': col+1,
                            'date': date_str,
                            'target': target_val,
                            'actual': actual_val,
                            'final': target_val_hkd
                        })
                        print(f"[需修改] 第{row+1}行，第{page_col+1}格（全局第{col+1}天），原始目标: {target_val}，换算港币: {target_val_hkd}，实际: {actual_val}，最终填入: {target_val_hkd}")
                        self.fill_single_cell(row, page_col, col+1, target_val, date_str, page_idx)
                        has_change = True
            page_start += 格数
            page_idx += 1
            if page_start < n:
                print("本页已填完，点击'后两周'按钮，进入下一页...")
                self.click_next_two_weeks()
                time.sleep(2)
        # 新增：统一输出所有需要被修改的格子
        print("\n==============================")
        if need_modify_cells:
            print("需要修改的格子如下：")
            for cell in need_modify_cells:
                print(f"第{cell['row']}行，第{cell['col']}列（{cell['date']}），目标: {cell['target']}，实际: {cell['actual']}，最终填入: {cell['final']}")
        else:
            print("所有格子均已匹配，无需修改")
        print("==============================\n")
        print("=== 自动化页面填充完成 ===\n")
        return has_change  # 返回本轮是否有格子被修改

    def fill_four_rows_price_by_page(self, price_data: dict, use_manual_actual=False, manual_actual_matrix=None):
        """
        斜三角三晚连住+关房自动化填充主流程，只采集-比对-填充一次。
        :param use_manual_actual: 是否使用手动传入的实际矩阵
        :param manual_actual_matrix: 手动传入的实际矩阵（4行N列）
        """
        self.get_today_col_index()
        matrix, date_list = self.generate_price_matrix(price_data)
        n = len(date_list)
        rows = 4
        # 打印要填入的价格矩阵
        print("\n=== 要填入的价格矩阵（人民币） ===")
        for row in matrix:
            print(' '.join([str(x) for x in row]))
        print("=== 价格矩阵打印结束 ===\n")
        # 打印换算成港币的价格矩阵
        print("=== 要填入的价格矩阵（港币） ===")
        for row in matrix:
            print(' '.join([str(self.currency_cny_to_hkd(x) if x not in (None, 9999) else x) for x in row]))
        print("=== 港币价格矩阵打印结束 ===\n")
        # 只采集一次
        if use_manual_actual and manual_actual_matrix is not None:
            actual_matrix = manual_actual_matrix
        else:
            actual_matrix = self.get_actual_price_matrix(date_list)
        self.compare_and_fill_matrix(matrix, actual_matrix, date_list)

    def click_next_two_weeks(self):
        """
        点击'后两周'按钮，并选择房型
        """
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行click_next_two_weeks")
        next_btn = self.page.ele('text:后两周')
        if next_btn:
            next_btn.click()
            time.sleep(2)
            # 每次点击后两周都选择房型
            if self.tongcheng_hotel_room_name:
                self.select_room_by_name(self.tongcheng_hotel_room_name)
            return True
        else:
            print("未找到'后两周'按钮，无法继续维护后续日期！")
            return False

    def click_prev_two_weeks(self):
        """
        点击'前两周'按钮，不选择房型
        """
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行click_prev_two_weeks")
        prev_btn = self.page.ele('text:前两周')
        if prev_btn:
            prev_btn.click()
            time.sleep(1)
            return True
        else:
            print("未找到'前两周'按钮！")
            return False

    @staticmethod
    def fill_missing_dates(price_data, days=30):
        """
        补全今天起连续days天的所有日期，没有价格的日期补None。
        """
        today = datetime.date.today()
        filled = {}
        for i in range(days):
            date_str = (today + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
            filled[date_str] = price_data.get(date_str, None)
        return filled

    def get_first_room_price_data(hotel_code: str, days: int = 30,target_room_name="豪华房（南楼）(大床)") -> dict:
        """
        滑动窗口三晚连住查询：每次查checkIn=当天，checkOut=当天+3天，窗口每天滑动，保证每一天都能拿到三晚连住价。
        只保留check_in当天的价格。
        """
        result = {}
        today = datetime.date.today()
        url = "http://139.199.168.35:8080/getPrice"
        target_room_name = target_room_name
        for i in range(days - 2):  # 最后两天无法再查三晚
            check_in = today + datetime.timedelta(days=i)
            check_out = check_in + datetime.timedelta(days=3)
            payload = json.dumps({
                "hotelCode": hotel_code,
                "checkInDate": check_in.strftime('%Y-%m-%d'),
                "checkOutDate": check_out.strftime('%Y-%m-%d'),
                "personCount": "1",
                "roomCount": "1",
                "channel": "同城"
            })
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.request("GET", url, headers=headers, data=payload)
            try:
                resp_json = json.loads(response.text)
                data = resp_json.get('data', {})
                room_list = data.get("roomList", [])
                if not room_list:
                    continue
                target_room = None
                for room in room_list:
                    if room.get("roomName") == target_room_name:
                        target_room = room
                        break
                if not target_room:
                    print(f"未找到房型：{target_room_name}")
                    continue
                price_list = target_room.get("priceList", [])
                if price_list:
                    avg_price_list = price_list[0].get("averagePriceList", [])
                    # 只取本次窗口的第一天（check_in）的价格
                    if avg_price_list:
                        for day_price in avg_price_list:
                            for date_str, price in day_price.items():
                                if date_str == check_in.strftime('%Y-%m-%d'):
                                    result[date_str] = int(float(price))
            except Exception as e:
                print(f"解析接口数据出错: {e}")
        return result

    def parse_page_date(self, date_text, column_index=None):
        """
        解析页面日期文本，将简化格式转换为完整日期。
        页面显示格式：今日 7 8 9 10 11
        处理零点后的时间窗口问题：过了零点后，原来的"今日"变成数字，新的"今日"要等到早上7点才出现。
        :param date_text: 页面显示的日期文本
        :param column_index: 列索引，用于判断是否为第一列
        :return: 完整的日期字符串 YYYY-MM-DD
        """
        if date_text == '今日':
            return datetime.date.today().strftime('%Y-%m-%d')

        # 处理数字日期（如 7, 8, 9, 10, 11）
        try:
            day_num = int(date_text)
            today = datetime.date.today()

            # 特殊处理：如果是第一列（column_index=2）且数字比今天小1，可能是零点后的时间窗口
            if column_index == 2 and day_num == today.day - 1:
                # 这可能是零点后的情况，第一列显示的是昨天的日期
                # 但实际上应该是今天的日期
                print(f"检测到可能的零点后时间窗口：第一列显示 '{date_text}'，但今天是 {today.day} 号")
                return today.strftime('%Y-%m-%d')

            # 智能判断日期序列
            target_date = self.smart_date_inference(day_num, today, column_index)
            return target_date.strftime('%Y-%m-%d')

        except ValueError:
            # 不是数字，返回原文本
            return date_text

    # 新增：选择房型方法
    def select_room_by_name(self, room_name):
        """
        选择指定房型
        """
        room_type_sele = 'x://*[@id="product-app"]/div/div[3]/div[1]/form/div[1]/div/div/div[1]'
        self.page.ele(room_type_sele).click()
        # 获取下拉列表中的所有选项
        li_elements = self.page.eles(
            "x://body/div[@class='el-select-dropdown el-popper is-multiple']/div[@class='el-scrollbar']/div[@class='el-select-dropdown__wrap el-scrollbar__wrap']/ul[@class='el-scrollbar__view el-select-dropdown__list']/li")
        # 遍历所有选项，查找匹配的房型名称
        for i, li_ele in enumerate(li_elements):
            room_name_option = li_ele.ele('x:/div/div').text
            # print(f"选项{i + 1}: {room_name_option}")
            if room_name == room_name_option:
                # 找到匹配的房型，点击选择
                li_ele.click()
                self.page.ele("x://div[@class='additionBtns flex items-center justify-center']/div[2]").click()  # 点击确定
                break

    def get_page_date_list(self, target_days=30):
        """
        获取页面表头的实际日期列表，根据目标天数智能翻页。
        :param target_days: 目标天数，默认30天
        :return: 完整的日期列表
        """
        all_dates = []
        current_page = 0
        max_pages = (target_days + 13) // 14  # 每页约14天，计算需要的页数

        print(f"目标获取{target_days}天数据，预计需要{max_pages}页")

        while current_page < max_pages and len(all_dates) < target_days:
            page_dates = []
            # 获取当前页的日期
            for i in range(2, 16):  # th[2] 到 th[15]
                try:
                    date_text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                    if date_text:
                        # 使用新的日期解析函数
                        parsed_date = self.parse_page_date(date_text.strip())
                        page_dates.append(parsed_date)
                        print(f"  列{i}: '{date_text}' -> '{parsed_date}'")
                except Exception as e:
                    print(f"  列{i}: 解析失败 - {e}")
                    break

            if not page_dates:
                print(f"第{current_page + 1}页未获取到日期数据，停止翻页")
                break

            all_dates.extend(page_dates)
            current_page += 1
            print(f"第{current_page}页获取到{len(page_dates)}个日期，累计{len(all_dates)}个")

            # 如果还需要更多数据且不是最后一页，点击下一页
            if len(all_dates) < target_days and current_page < max_pages:
                print(f"点击'后两周'进入第{current_page + 1}页")
                if not self.click_next_two_weeks():
                    print("无法点击'后两周'按钮，停止翻页")
                    break
                time.sleep(2)

        # 回到第一页
        if current_page > 1:
            print(f"回到第一页，需要点击{current_page - 1}次'前两周'")
            for i in range(current_page - 1):
                self.click_prev_two_weeks()
                time.sleep(1)

        print(f"最终获取到{len(all_dates)}个日期: {all_dates[:5]}...{all_dates[-5:] if len(all_dates) > 5 else ''}")
        return all_dates[:target_days]  # 只返回目标天数的数据

    def get_actual_price_matrix_by_date(self, target_dates, rows=4):
        """
        基于日期匹配获取实际价格矩阵，避免位置错位问题。
        优化翻页逻辑，30天只需点击2次后两周。
        :param target_dates: 目标日期列表
        :param rows: 行数
        :return: 价格矩阵字典 {date: [row1_price, row2_price, ...]}
        """
        price_matrix = {}
        current_page = 0
        processed_dates = set()
        max_pages = (len(target_dates) + 13) // 14  # 每页约14天

        print(f"开始获取{len(target_dates)}个日期的价格数据，预计需要{max_pages}页")

        while len(processed_dates) < len(target_dates) and current_page < max_pages:
            # 获取当前页的日期列表
            page_dates = []
            date_to_col = {}  # 日期到列号的映射

            for i in range(2, 16):  # th[2] 到 th[15]
                try:
                    date_text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                    if date_text:
                        # 使用新的日期解析函数
                        date_str = self.parse_page_date(date_text.strip())

                        if date_str in target_dates and date_str not in processed_dates:
                            page_dates.append(date_str)
                            date_to_col[date_str] = i
                            print(f"    列{i}: '{date_text}' -> '{date_str}' (匹配)")
                        else:
                            print(f"    列{i}: '{date_text}' -> '{date_str}' (跳过)")
                except Exception as e:
                    print(f"    列{i}: 解析失败 - {e}")
                    continue

            print(f"第{current_page + 1}页找到{len(page_dates)}个目标日期: {page_dates}")

            # 获取当前页匹配日期的价格
            for date_str in page_dates:
                col_idx = date_to_col[date_str]
                date_prices = []

                for row in range(rows):
                    xpath_num = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]/span/span/div/p[2]'
                    xpath_invalid = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]/p'
                    xpath_base = f'x://*[@id="product-app"]/div/div[3]/div[4]/table/tbody/tr[{row+2}]/td[{col_idx}]/div[3]'

                    val = None
                    try:
                        val = self.page.ele(xpath_num, timeout=0.5).text
                    except Exception:
                        try:
                            val = self.page.ele(xpath_invalid, timeout=0.5).text
                        except Exception:
                            try:
                                val = self.page.ele(xpath_base, timeout=0.5).text
                            except Exception:
                                val = None

                    if val is None or (isinstance(val, str) and (val.strip() in ['无效', '无房', ''])):
                        date_prices.append(9999)
                    else:
                        try:
                            date_prices.append(int(val))
                        except Exception:
                            date_prices.append(val)

                price_matrix[date_str] = date_prices
                processed_dates.add(date_str)

            current_page += 1

            # 如果还有未处理的日期且未达到最大页数，翻到下一页
            if len(processed_dates) < len(target_dates) and current_page < max_pages:
                print(f"需要更多数据，点击'后两周'进入第{current_page + 1}页")
                if not self.click_next_two_weeks():
                    print("无法点击'后两周'，停止翻页")
                    break
                time.sleep(2)
            else:
                print(f"已处理{len(processed_dates)}/{len(target_dates)}个日期，停止翻页")
                break

        # 回到第一页
        if current_page > 1:
            print(f"回到第一页，需要点击{current_page - 1}次'前两周'")
            for i in range(current_page - 1):
                self.click_prev_two_weeks()
                time.sleep(1)

        print(f"最终获取到{len(processed_dates)}个日期的价格数据")
        return price_matrix

    def smart_price_maintenance(self, price_data: dict, rows=4, threshold=0.05):
        """
        智能价格维护：基于日期匹配，只更新有变化的格子。
        :param price_data: 目标价格数据 {date: price}
        :param rows: 行数
        :param threshold: 价格差异阈值
        """
        print(f"\n=== 开始智能价格维护 ===")
        print(f"目标日期数量: {len(price_data)}")

        # 选择房型
        if self.tongcheng_hotel_room_name:
            self.select_room_by_name(self.tongcheng_hotel_room_name)
            print(f"已选择房型：{self.tongcheng_hotel_room_name}")
            time.sleep(2)

        # 生成目标价格矩阵
        target_matrix, date_list = self.generate_price_matrix(price_data, rows)
        print(f"生成目标矩阵，日期范围: {date_list[0]} 到 {date_list[-1]}")

        # 获取实际价格矩阵（基于日期匹配）
        actual_price_matrix = self.get_actual_price_matrix_by_date(date_list, rows)
        print(f"获取到实际价格数据，覆盖日期: {len(actual_price_matrix)}")

        # 按页面批量处理，避免重复翻页
        changes_made = self.batch_update_by_pages(target_matrix, actual_price_matrix, date_list, rows, threshold)

        print(f"\n=== 智能维护完成 ===")
        total_cells = len(date_list) * rows
        print(f"总格子数: {total_cells}, 更新格子数: {changes_made}, 跳过: {total_cells - changes_made}")
        print(f"更新效率: {((total_cells - changes_made) / total_cells * 100):.1f}% 的格子无需更新")

        return changes_made

    def batch_update_by_pages(self, target_matrix, actual_price_matrix, date_list, rows, threshold):
        """
        按页面批量处理更新，避免重复翻页。
        先分析所有需要更新的格子，然后按页面分组批量处理。
        """
        print(f"\n🔍 分析需要更新的格子...")

        # 步骤1: 分析所有需要更新的格子
        updates_needed = []  # [(date_str, row, target_price, reason), ...]

        for row in range(rows):
            for col, date_str in enumerate(date_list):
                target_price = target_matrix[row][col]
                actual_prices = actual_price_matrix.get(date_str, [9999] * rows)
                actual_price = actual_prices[row] if row < len(actual_prices) else 9999

                # 判断是否需要更新
                need_update, reason = self.check_if_update_needed(target_price, actual_price, threshold)

                if need_update:
                    updates_needed.append((date_str, row, target_price, reason))
                    print(f"  [需更新] 第{row+1}行 {date_str}: {reason} (目标:{target_price} 实际:{actual_price})")
                else:
                    print(f"  [跳过] 第{row+1}行 {date_str}: 无需更新 (目标:{target_price} 实际:{actual_price})")

        if not updates_needed:
            print("✅ 所有格子都是最新的，无需更新！")
            return 0

        print(f"\n📋 共找到 {len(updates_needed)} 个需要更新的格子")

        # 步骤2: 按页面分组需要更新的格子
        page_updates = self.group_updates_by_page(updates_needed, date_list)

        # 步骤3: 按页面批量执行更新
        total_changes = 0
        current_page = 0

        for page_idx, page_update_list in page_updates.items():
            if not page_update_list:
                continue

            print(f"\n📄 处理第 {page_idx + 1} 页，共 {len(page_update_list)} 个更新")

            # 导航到目标页面
            if page_idx != current_page:
                self.navigate_to_page(current_page, page_idx)
                current_page = page_idx
                time.sleep(2)  # 等待页面加载

            # 批量处理当前页面的所有更新
            page_changes = self.process_page_updates(page_update_list, page_idx)
            total_changes += page_changes

            print(f"  ✅ 第 {page_idx + 1} 页完成，更新了 {page_changes} 个格子")

        # 步骤4: 回到第一页
        if current_page > 0:
            print(f"\n🏠 回到第一页...")
            self.navigate_to_page(current_page, 0)

        return total_changes

    def check_if_update_needed(self, target_price, actual_price, threshold):
        """
        检查是否需要更新格子
        :return: (need_update: bool, reason: str)
        """
        if target_price is None or target_price == 9999:
            if actual_price is not None and actual_price != 9999:
                return True, "需设无效"
        elif actual_price is None or actual_price == 9999:
            return True, "需设有效价格"
        else:
            # 比较价格差异（转换为港币后比较）
            target_hkd = self.currency_cny_to_hkd(target_price)
            if not self.is_price_close(target_hkd, actual_price, threshold):
                return True, f"价格差异超过{int(threshold*100)}%"

        return False, "无需更新"

    def group_updates_by_page(self, updates_needed, date_list):
        """
        将需要更新的格子按页面分组
        :param updates_needed: [(date_str, row, target_price, reason), ...]
        :param date_list: 完整的日期列表
        :return: {page_idx: [updates_for_this_page], ...}
        """
        page_updates = {}
        dates_per_page = 14  # 每页约14个日期

        for date_str, row, target_price, reason in updates_needed:
            try:
                date_index = date_list.index(date_str)
                page_idx = date_index // dates_per_page

                if page_idx not in page_updates:
                    page_updates[page_idx] = []

                page_updates[page_idx].append((date_str, row, target_price, reason, date_index))
            except ValueError:
                print(f"⚠️  警告: 日期 {date_str} 不在日期列表中")

        # 打印分组结果
        print(f"\n📊 更新分组结果:")
        for page_idx in sorted(page_updates.keys()):
            updates = page_updates[page_idx]
            dates_in_page = list(set([update[0] for update in updates]))
            print(f"  第 {page_idx + 1} 页: {len(updates)} 个更新，涉及日期: {dates_in_page[:3]}{'...' if len(dates_in_page) > 3 else ''}")

        return page_updates

    def navigate_to_page(self, current_page, target_page):
        """
        从当前页面导航到目标页面
        """
        if current_page == target_page:
            return

        if target_page > current_page:
            # 向前翻页
            pages_to_forward = target_page - current_page
            print(f"  📖 向前翻 {pages_to_forward} 页...")
            for i in range(pages_to_forward):
                if not self.click_next_two_weeks():
                    print(f"  ❌ 翻页失败，停在第 {current_page + i + 1} 页")
                    break
                time.sleep(1)
        else:
            # 向后翻页
            pages_to_backward = current_page - target_page
            print(f"  📖 向后翻 {pages_to_backward} 页...")
            for i in range(pages_to_backward):
                if not self.click_prev_two_weeks():
                    print(f"  ❌ 翻页失败，停在第 {current_page - i - 1} 页")
                    break
                time.sleep(1)

    def process_page_updates(self, page_update_list, page_idx):
        """
        处理当前页面的所有更新
        :param page_update_list: [(date_str, row, target_price, reason, date_index), ...]
        :param page_idx: 当前页面索引
        :return: 实际更新的格子数
        """
        # 获取当前页面的日期到列的映射
        date_to_col = self.get_current_page_date_mapping()

        changes_made = 0
        for date_str, row, target_price, reason, date_index in page_update_list:
            if date_str in date_to_col:
                col_idx = date_to_col[date_str]
                page_col = col_idx - 2  # 转换为页面相对列号

                print(f"    🔧 更新 第{row+1}行 {date_str} (列{col_idx}): {reason}")
                try:
                    self.fill_single_cell(row, page_col, col_idx, target_price, date_str, page_idx)
                    changes_made += 1
                except Exception as e:
                    print(f"    ❌ 更新失败: {e}")
            else:
                print(f"    ⚠️  警告: 在当前页面未找到日期 {date_str}")

        return changes_made

    def get_current_page_date_mapping(self):
        """
        获取当前页面的日期到列号的映射
        :return: {date_str: col_idx, ...}
        """
        date_to_col = {}

        for i in range(2, 16):  # th[2] 到 th[15]
            try:
                date_text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{i}]/div/span[1]/span').text
                if date_text:
                    date_str = self.parse_page_date(date_text.strip())
                    date_to_col[date_str] = i
            except Exception:
                continue

        return date_to_col

    def update_single_cell_by_date(self, date_str, row, target_price):
        """
        根据日期定位并更新单个格子。
        :param date_str: 目标日期
        :param row: 行号
        :param target_price: 目标价格
        """
        # 查找日期所在的页面和列
        page_idx = 0
        found = False

        while not found and page_idx < 5:  # 最多查找5页
            # 在当前页查找日期
            for col_idx in range(2, 16):
                try:
                    date_text = self.page.ele(f'x://*[@id="product-app"]/div/div[3]/div[4]/table/thead/tr/th[{col_idx}]/div/span[1]/span').text
                    if date_text:
                        # 使用新的日期解析函数
                        current_date = self.parse_page_date(date_text.strip())

                        if current_date == date_str:
                            # 找到目标日期，更新格子
                            page_col = col_idx - 2  # 转换为页面相对列号
                            print(f"找到目标日期 {date_str} 在第{page_idx + 1}页第{col_idx}列")
                            self.fill_single_cell(row, page_col, col_idx, target_price, date_str, page_idx)
                            found = True
                            break
                except Exception as e:
                    print(f"查找日期失败: {e}")
                    continue

            if not found:
                # 翻到下一页继续查找
                if self.click_next_two_weeks():
                    time.sleep(2)
                    page_idx += 1
                else:
                    break

        # 回到第一页
        for i in range(page_idx):
            self.click_prev_two_weeks()
            time.sleep(1)

        if not found:
            print(f"警告: 未找到日期 {date_str} 对应的格子")

    # 新增：按日期比对和维护价格的方法（保留原有方法以兼容）
    def compare_and_fill_matrix_by_date(self, page_date_list, actual_price_matrix, target_price_dict, threshold=0.05):
        """
        按页面日期与目标价格一一对应比对和维护，支持多行。
        :param page_date_list: 页面表头日期列表，如['2025-07-07', ...]
        :param actual_price_matrix: 页面实际价格矩阵，list of list，行数为房型数，列数为日期数
        :param target_price_dict: 目标价格字典，如{'2025-07-07': [1200, 1300, ...], ...}
        :param threshold: 价格相差阈值
        """
        row_count = len(actual_price_matrix)
        col_count = len(page_date_list)
        for row in range(row_count):
            for col, date_str in enumerate(page_date_list):
                # 获取目标价和实际价
                target_price_list = target_price_dict.get(date_str, None)
                target_price = None
                if target_price_list and row < len(target_price_list):
                    target_price = target_price_list[row]
                actual_price = actual_price_matrix[row][col] if col < len(actual_price_matrix[row]) else None
                # 价格都为None或9999时无需操作
                if (target_price is None or str(target_price) == '9999') and (actual_price is None or str(actual_price) == '9999'):
                    print(f"[无需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，均为无效/无房，无需修改")
                    continue
                # 目标为None/9999但实际有价格，需设无效
                if (target_price is None or str(target_price) == '9999') and (actual_price is not None and str(actual_price) != '9999'):
                    print(f"[需设无效] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)
                    continue
                # 目标有价格，实际无效/无房，需维护
                if (target_price is not None and str(target_price) != '9999') and (actual_price is None or str(actual_price) == '9999'):
                    print(f"[需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，最终填入: {target_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)
                    continue
                # 目标和实际都有价格，判断是否在阈值内
                try:
                    t = int(target_price)
                    a = int(actual_price)
                    if t == 0:
                        close = (a == 0)
                    else:
                        close = abs(t - a) / abs(t) <= threshold
                except Exception:
                    close = False
                if close:
                    print(f"[无需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，差值在{int(threshold*100)}%以内，无需修改")
                else:
                    print(f"[需修改] 第{row+1}行 日期: {date_str}，目标: {target_price}，实际: {actual_price}，最终填入: {target_price}")
                    self.fill_single_cell(row, col, col+1, target_price, date_str, page_idx=0)

# ================== 新增：测试用例 ==================
if __name__ == "__main__":
    hotel_code = "huwipk843531"
    hotel_name = "香港港丽酒店(康莱德)"
    badazhou_hotel_room_name = '豪华山景客房-双床(双床)'
    tongcheng_hotel_room_name = '豪华山景双床房'

    days = 30
    page = ChromiumPage()
    maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)

    # 使用新的智能维护方法
    while True:
        print("\n==============================")
        print(f"开始智能价格维护流程，当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 获取价格数据
        price_data = PriceMaintainer.get_first_room_price_data(hotel_code, days, badazhou_hotel_room_name)
        price_data = PriceMaintainer.fill_missing_dates(price_data, days)

        # 使用智能维护方法，只更新有变化的格子
        changes_made = maintainer.smart_price_maintenance(price_data)

        if changes_made == 0:
            print("所有价格已是最新，无需更新！")
        else:
            print(f"本轮更新了 {changes_made} 个格子")

        print("==============================\n")

        # 等待下一轮检查（每2小时检查一次）
        time.sleep(2 * 60 * 60)

    # 以下是测试代码，可以取消注释进行测试
    # print("\n===== 智能维护测试用例 =====")
    # test_price_data = {
    #     '2025-07-07': 1200,
    #     '2025-07-08': 1100,
    #     '2025-07-09': None,  # 设为无效
    #     '2025-07-10': 1400
    # }
    # test_maintainer = PriceMaintainer(page, hotel_code, hotel_name, days, tongcheng_hotel_room_name)
    # test_maintainer.smart_price_maintenance(test_price_data)
    # print("===== 测试用例结束 =====\n")