import json
import time
import os
from typing import Dict, Any, List, Optional
from DrissionPage import ChromiumPage
from tongcheng.pusher.base_pusher import BasePusher
from tongcheng.pusher.tongcheng.hotel_operator import TongchengHotelOperator
from tongcheng.pusher.tongcheng.room_operator import Tong<PERSON>RoomOperator
from tongcheng.pusher.tongcheng.price_operator import TongchengPriceOperator
from tongcheng.pusher.tongcheng.dialog_operator import TongchengDialogOperator
from tongcheng.pusher.tongcheng.page_navigator import TongchengPageNavigator
from tongcheng.pusher.tongcheng.currency_converter import TongchengCurrencyConverter


class TongchengPusher(BasePusher):
    """
    同程推送器实现，集成DrissionPage自动化推送价格到同程平台
    """

    def __init__(self, cookies_path: str = r"cookies.json", headless: bool = False):
        super().__init__()
        self.cookies_path = cookies_path
        self.headless = headless
        self.page: Optional[ChromiumPage] = None
        self.is_connected = False
        self.base_url = "https://ebooking.elong.com"
        self.dashboard_url = "https://ebooking.elong.com/ebkcommon/dashboard#/dashboard"
        # 组合各操作类
        self.hotel_operator = None
        self.room_operator = None
        self.price_operator = None
        self.dialog_operator = None
        self.page_navigator = None
        self.currency_converter = None

    def connect(self) -> bool:
        """
        建立连接，初始化浏览器并登录同程
        """
        try:
            self.logger.info("开始连接同程平台...")
            print("DEBUG: cookies_path =", self.cookies_path)
            print("DEBUG: file exists =", os.path.exists(self.cookies_path))
            if not os.path.exists(self.cookies_path):
                self.logger.error(f"Cookies文件不存在: {self.cookies_path}")
                return False
            self.page = ChromiumPage()
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            self.page.get(self.base_url)
            self.page.set.cookies(cookies)
            self.page.get(self.dashboard_url)
            time.sleep(3)
            # 初始化操作类
            self.hotel_operator = TongchengHotelOperator(self.page, self.logger)
            self.room_operator = TongchengRoomOperator(self.page, self.logger)
            self.currency_converter = TongchengCurrencyConverter()
            self.price_operator = TongchengPriceOperator(self.page, self.logger, self.currency_converter)
            self.dialog_operator = TongchengDialogOperator(self.page, self.logger)
            self.page_navigator = TongchengPageNavigator(self.page, self.logger)
            if self._verify_login():
                self.is_connected = True
                self.logger.info("成功连接并登录同程平台")
                return True
            else:
                self.logger.error("登录验证失败")
                return False
        except Exception as e:
            self.logger.error(f"连接同程平台失败: {self.format_error_message(e)}")
            return False

    def disconnect(self) -> bool:
        try:
            if self.page:
                self.page.quit()
                self.page = None
            self.is_connected = False
            self.logger.info("已断开与同程平台的连接")
            return True
        except Exception as e:
            self.logger.error(f"断开连接失败: {self.format_error_message(e)}")
            return False

    def push_price(self, data: Dict[str, Any]) -> bool:
        if not self.validate_data(data):
            self.logger.error("数据验证失败")
            return False
        hotel_name = data.get("同程酒店名称", "")
        room_data = data.get("房型数据", [])


        self.log_push_attempt(hotel_name, len(room_data))
        try:
            # 进入酒店
            if not self.hotel_operator.find_and_enter_hotel(hotel_name):
                self.log_push_result(hotel_name, False, "无法找到或进入酒店")
                return False

            time.sleep(3)
            self.logger.info(f"开始推送价格到同程酒店: {hotel_name}")

            try:
                for room in room_data:
                    room_name = room.get("同程房型名称", "")
                    print(f'正在维护{hotel_name}-{room_name}价格')
                    # 构建价格数据
                    price_data = self._build_price_data(room)
                    if not price_data:
                        self.log_push_result(hotel_name, False, "价格数据为空")
                        return False
                    # 先点击房价维护
                    price_maintain_btn = self.page.ele(
                        'x://*[@id="common_app"]/div/div[3]/div[1]/div/div/div[2]/div[1]/div/ul[4]/li')
                    if price_maintain_btn:
                        price_maintain_btn.click()
                        time.sleep(2)
                    else:
                        self.log_push_result(hotel_name, False, "未找到房价维护按钮")
                        return False
                    # 推送每个价格
                    success = self.price_operator.fill_price(price_data, room_name)
                    if success:
                        print(f'成功维护{hotel_name}-{room_name}价格')
            except Exception as e:
                print(f'{hotel_name}-{room_name}价格维护失败：{e}')


        except Exception as e:
            error_msg = self.format_error_message(e)
            self.log_push_result(hotel_name, False, error_msg)
            return False

    def _verify_login(self) -> bool:
        try:
            time.sleep(2)
            return True
        except Exception:
            return False

    def _build_price_data(self, room_data):
        data = room_data['价格数据']
        return data[0]

