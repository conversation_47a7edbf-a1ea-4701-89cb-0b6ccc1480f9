from typing import Dict, Any, List
from ..tools.logger import get_pusher_logger
from tongcheng.pusher.tongcheng.tongcheng_pusher import TongchengPusher

# 使用统一日志管理器
logger = get_pusher_logger()

class PusherManager:
    """
    推送管理器，负责管理各种推送器和推送逻辑。
    """
    def __init__(self, error_handler=None, performance_monitor=None, cookies_path: str = "test/cookies.json", use_smart_maintenance=True):
        self.error_handler = error_handler
        self.performance_monitor = performance_monitor
        self.use_smart_maintenance = use_smart_maintenance  # 新增：是否使用智能维护

        # 初始化同程推送器
        self.pusher = TongchengPusher(cookies_path=cookies_path)
        self.is_connected = False

    def connect(self) -> bool:
        """
        建立推送器连接
        """
        try:
            logger.info("推送管理器：尝试建立连接...")
            if self.pusher.connect():
                self.is_connected = True
                logger.info("推送管理器：连接成功")
                return True
            else:
                logger.error("推送管理器：连接失败")
                return False
        except Exception as e:
            logger.error(f"推送管理器连接异常: {str(e)}")
            return False

    def disconnect(self) -> bool:
        """
        断开推送器连接
        """
        try:
            if self.pusher.disconnect():
                self.is_connected = False
                logger.info("推送管理器：已断开连接")
                return True
            else:
                logger.warning("推送管理器：断开连接时出现问题")
                return False
        except Exception as e:
            logger.error(f"推送管理器断开连接异常: {str(e)}")
            return False

    def push_price_data(self, hotel_name: str, matched_rooms: List[Dict]) -> bool:
        """推送价格数据"""
        try:
            # 确保连接已建立
            if not self.is_connected:
                logger.info("推送器未连接，尝试建立连接...")
                if not self.connect():
                    if self.error_handler:
                        self.error_handler.record_error("推送器连接失败", {
                            "酒店名称": hotel_name,
                            "房型数量": len(matched_rooms),
                            "错误原因": "无法建立推送器连接"
                        })
                    return False

            push_data = {
                "同程酒店名称": hotel_name,
                "房型数据": matched_rooms
            }

            logger.info(f"推送管理器：开始推送酒店 {hotel_name} 的价格数据")

            # 调用推送器推送数据
            result = self.pusher.push_price(push_data)

            if result:
                if self.performance_monitor:
                    self.performance_monitor.increment_counter("successful_pushes", len(matched_rooms))
                logger.info(f"推送成功: {hotel_name}, 房型数量: {len(matched_rooms)}")
                return True
            else:
                if self.performance_monitor:
                    self.performance_monitor.increment_counter("failed_pushes", len(matched_rooms))

                if self.error_handler:
                    self.error_handler.record_error("推送失败", {
                        "酒店名称": hotel_name,
                        "房型数量": len(matched_rooms),
                        "错误原因": "推送器返回失败"
                    })
                return False

        except Exception as e:
            if self.performance_monitor:
                self.performance_monitor.increment_counter("failed_pushes", len(matched_rooms))

            if self.error_handler:
                self.error_handler.record_error("推送异常", {
                    "酒店名称": hotel_name,
                    "房型数量": len(matched_rooms),
                    "错误原因": str(e)
                })
            logger.error(f"推送数据异常: {str(e)}")
            return False

    def push_multiple_hotels(self, hotels_data: List[Dict]) -> Dict[str, bool]:
        """
        批量推送多个酒店的数据
        """
        results = {}

        try:
            # 确保连接已建立
            if not self.is_connected:
                if not self.connect():
                    logger.error("批量推送：无法建立连接")
                    return {hotel.get("同程酒店名称", "unknown"): False for hotel in hotels_data}

            for hotel_data in hotels_data:
                hotel_name = hotel_data.get("同程酒店名称", "")
                matched_rooms = hotel_data.get("房型数据", [])

                if hotel_name and matched_rooms:
                    result = self.push_price_data(hotel_name, matched_rooms)
                    results[hotel_name] = result
                else:
                    logger.warning(f"跳过无效的酒店数据: {hotel_name}")
                    results[hotel_name] = False

        except Exception as e:
            logger.error(f"批量推送异常: {str(e)}")

        return results

    def get_pusher_status(self) -> Dict[str, Any]:
        """
        获取推送器状态
        """
        return {
            "is_connected": self.is_connected,
            "pusher_type": type(self.pusher).__name__,
            "cookies_path": getattr(self.pusher, 'cookies_path', 'unknown')
        }

    def reset_connection(self) -> bool:
        """
        重置连接
        """
        logger.info("推送管理器：重置连接")

        # 先断开现有连接
        if self.is_connected:
            self.disconnect()

        # 重新建立连接
        return self.connect()

    def __enter__(self):
        """上下文管理器：进入"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器：退出"""
        self.disconnect()