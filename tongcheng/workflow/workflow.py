from typing import List, Dict, Any
from ..tools.logger import get_workflow_logger, LoggerManager
from ..models.hotel_models import Hotel, RoomType
from ..models.mapping_models import RoomMapping
from ..models.price_models import PriceCalendar
from ..config_manager import get_feishu_config
from ..matcher.feishu.feishu_hotel_name_getter import HotelNameGetter, safe_strip, parse_rich_text
from ..matcher.feishu.feishu_room_name_getter import RoomNameGetter
from ..fetcher.badazhou_fetcher import BadazhouFetcher
from .data_processor import DataProcessor
from .pusher_manager import PusherManager
from datetime import datetime, timedelta

# 使用统一日志管理器
logger = get_workflow_logger()

class WorkflowCoordinator:
    """
    流程协调模块，负责协调任务流程。
    """
    def __init__(self, error_handler=None, performance_monitor=None, use_smart_maintenance=True):
        self.error_handler = error_handler
        self.performance_monitor = performance_monitor
        self.use_smart_maintenance = use_smart_maintenance  # 新增：是否使用智能维护

        # 初始化配置
        self.config = get_feishu_config()

        # 初始化组件
        self._init_components()

    def _init_components(self):
        """初始化各个组件"""
        hotel_config = self.config["hotel_table"]
        room_config = self.config["room_table"]

        # 初始化飞书数据获取器
        self.hotel_getter = HotelNameGetter(
            app_token=hotel_config["app_token"],
            table_id=hotel_config["table_id"],
            view_id=hotel_config["view_id"],
            use_tenant_token=hotel_config["use_tenant_token"]
        )

        self.room_getter = RoomNameGetter(
            app_token=room_config["app_token"],
            table_id=room_config["table_id"],
            view_id=room_config["view_id"],
            use_tenant_token=room_config["use_tenant_token"]
        )

        # 初始化其他组件
        self.fetcher = BadazhouFetcher()
        self.data_processor = DataProcessor(self.error_handler, self.performance_monitor)
        # 初始化推送管理器，使用test目录下的cookies.json
        self.pusher_manager = PusherManager(
            error_handler=self.error_handler,
            performance_monitor=self.performance_monitor,
            cookies_path=r"D:\mycode\crawler\project\tongcheng_hotel\tongcheng\cookies.json"
        )

    def execute_main_workflow(self) -> Dict[str, Any]:
        """执行主工作流程"""
        logger.info("=== 开始执行主工作流程 ===")

        try:
            # 1. 获取飞书酒店数据
            hotels_data = self._get_hotels_data()

            # 2. 获取飞书房型映射关系
            room_mappings = self._get_room_mappings()

            # 3. 获取日期范围
            start_date, days = self.data_processor.get_date_range(self.config)
            logger.info(f"查询日期范围: {start_date} 起 {days} 天")

            # 新增：生成date_list
            date_list = [(datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days)]

            # 4. 处理每个酒店
            self._process_hotels(hotels_data, room_mappings, start_date, days, date_list)

            logger.info("=== 主工作流程执行完成 ===")
            return {"status": "success", "message": "工作流程执行成功"}

        except Exception as e:
            logger.error(f"主工作流程执行异常: {str(e)}")
            if self.error_handler:
                self.error_handler.record_error("工作流程异常", {"错误原因": str(e)})
            raise e
        finally:
            # 确保断开推送器连接
            try:
                if hasattr(self, 'pusher_manager'):
                    self.pusher_manager.disconnect()
            except Exception as e:
                logger.warning(f"断开推送器连接时出现问题: {str(e)}")

    def _get_hotels_data(self) -> List[Dict]:
        """获取飞书酒店数据"""
        logger.info("步骤1: 获取飞书酒店数据")
        if self.error_handler:
            hotels_data = self.error_handler.retry_with_config(
                self.hotel_getter.get_hotel_names,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"]
            )
        else:
            hotels_data = self.hotel_getter.get_hotel_names()

        logger.info(f"获取到 {len(hotels_data)} 个酒店")
        return hotels_data

    def _get_room_mappings(self) -> List[Dict]:
        """获取飞书房型映射关系"""
        logger.info("步骤2: 获取飞书房型映射关系")
        if self.error_handler:
            room_mappings = self.error_handler.retry_with_config(
                self.room_getter.get_room_names,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"]
            )
        else:
            room_mappings = self.room_getter.get_room_names()

        logger.info(f"获取到 {len(room_mappings)} 个房型映射关系")
        return room_mappings

    def _process_hotels(self, hotels_data: List[Dict], room_mappings: List[Dict],
                       start_date: str, days: int, date_list: List[str]):
        """处理所有酒店"""
        for hotel in hotels_data:
            if self.performance_monitor:
                self.performance_monitor.increment_counter("processed_hotels")
            if not self.data_processor.validate_hotel_data(hotel):
                continue
            hotel_code = parse_rich_text(hotel.get("八大洲酒店编码", ""))
            hotel_name = parse_rich_text(hotel.get("八大洲酒店名称", ""))
            tongcheng_hotel_name = parse_rich_text(hotel.get("同程酒店名称", ""))
            hotel_code = str(hotel_code).strip() if hotel_code is not None else ""
            hotel_name = str(hotel_name).strip() if hotel_name is not None else ""
            tongcheng_hotel_name = str(tongcheng_hotel_name).strip() if tongcheng_hotel_name is not None else ""
            logger.info(f"处理酒店: {hotel_name} ({hotel_code})")
            try:
                hotel_price_data = self._get_hotel_price_data(hotel_code, hotel_name, start_date, days)
                if not hotel_price_data:
                    continue
                processed_hotels = self.data_processor.process_badazhou_data(hotel_price_data, date_list)
                if not processed_hotels:
                    if self.error_handler:
                        self.error_handler.record_error("数据处理失败", {
                            "酒店名称": hotel_name,
                            "酒店编码": hotel_code,
                            "错误原因": "数据处理器返回空结果"
                        })
                    continue
                # 直接用processed_hotels作为房型列表
                all_rooms = processed_hotels
                if not all_rooms:
                    if self.error_handler:
                        self.error_handler.record_error("房型数据为空", {
                            "酒店名称": hotel_name,
                            "酒店编码": hotel_code,
                            "错误原因": "未获取到房型数据"
                        })
                    continue
                hotel_room_mappings = self.data_processor.filter_hotel_room_mappings(
                    room_mappings, hotel_name
                )
                logger.info(f"开始房型匹配，酒店: {hotel_name}")
                matched_rooms = self.data_processor.match_rooms(all_rooms, hotel_room_mappings)
                if matched_rooms:
                    logger.info(f"推送价格数据，酒店: {tongcheng_hotel_name}")
                    self.pusher_manager.push_price_data(tongcheng_hotel_name, matched_rooms)
                else:
                    logger.warning(f"酒店 {hotel_name} 没有匹配到任何房型")
            except Exception as e:
                if self.error_handler:
                    self.error_handler.record_error("酒店处理异常", {
                        "酒店名称": hotel_name,
                        "酒店编码": hotel_code,
                        "错误原因": str(e)
                    })
                continue

    def _get_hotel_price_data(self, hotel_code: str, hotel_name: str,
                             start_date: str, days: int) -> List[Dict]:
        """获取酒店价格数据"""
        logger.info(f"获取酒店 {hotel_name} 的价格数据")

        if self.error_handler:
            price_result = self.error_handler.retry_with_config(
                self.fetcher.fetch_price_range_sliding_window,
                self.config["retry_config"]["max_retries"],
                self.config["retry_config"]["retry_interval"],
                [hotel_code], start_date, days
            )
        else:
            price_result = self.fetcher.fetch_price_range_sliding_window([hotel_code], start_date, days)

        hotel_price_data = price_result.get(hotel_code, [])
        if not hotel_price_data:
            if self.error_handler:
                self.error_handler.record_error("价格数据获取失败", {
                    "酒店名称": hotel_name,
                    "酒店编码": hotel_code,
                    "错误原因": "接口返回空数据"
                })
            return []

        return hotel_price_data

    def coordinate(self, tasks):
        """
        协调任务流程，tasks 为任务列表。
        """
        logger.info(f"开始协调 {len(tasks)} 个任务")
        for task in tasks:
            try:
                if callable(task):
                    task()
                else:
                    logger.warning(f"任务不可执行: {task}")
            except Exception as e:
                logger.error(f"任务执行失败: {str(e)}")
                if self.error_handler:
                    self.error_handler.record_error("任务执行失败", {"错误原因": str(e)})